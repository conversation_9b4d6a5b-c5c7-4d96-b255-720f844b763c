import 'package:flutter/material.dart';
import 'package:petcare/utlis/constants/colors.dart';

import '../../../../../common/widgets/Button/primarybutton.dart';
import '../../widgets/custom_text_field.dart';

class EditClientDetails extends StatefulWidget {
  const EditClientDetails({super.key});

  @override
  State<EditClientDetails> createState() => _EditClientDetailsState();
}

class _EditClientDetailsState extends State<EditClientDetails> {
  bool isMale = true;

  final Map<String, bool> switches = {
    "Neutered/ Spade": true,
    "Vaccinated": true,
    "Friendly with Dogs": true,
    "Friendly with Cats": false,
    "Friendly with Kids <10 year": true,
    "Friendly with Kids >10 year": true,
    "Microchipped": true,
    "Purebred": true,
    "Potty Trained": true,
    "Send Push Notifications": true,
    "Receive Emails?": true,
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Edit Client")),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Stack(
                  children: [
                    CircleAvatar(
                      radius: 40,
                      backgroundColor: Colors.blueGrey,
                      child: const Icon(Icons.camera_alt, color: Colors.white),
                    ),
                  ],
                ),
                const Spacer(),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children:  [
                    Text("Last Visit Date",
                      style:Theme.of(context).textTheme.bodyLarge!.copyWith(
                        color: AppColors.primary,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text("5/2/2021",
                      style:Theme.of(context).textTheme.bodyLarge!.copyWith(
                        color: AppColors.primary,
                        fontWeight:FontWeight.w600,
                      ),
                    ),
                  ],
                )
              ],
            ),
            const SizedBox(height: 20),
            CustomTextField(
              label: 'Client Name',
              hintText: "Your name",
              controller: TextEditingController(),
              focusNode: FocusNode(),
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(FocusNode()),
              maxLines: 1,
            ),
            const SizedBox(height: 10),
            CustomTextField(
              label: 'Client Email',
              hintText: "Your phone number",
              controller: TextEditingController(),
              focusNode: FocusNode(),
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(FocusNode()),
              maxLines: 1,
            ),
            const SizedBox(height: 10),
            CustomTextField(
              label: 'Client Phone Number',
              hintText: "Your phone number",
              controller: TextEditingController(),
              focusNode: FocusNode(),
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(FocusNode()),
              maxLines: 1,
            ),
            const SizedBox(height: 10),
            CustomTextField(
              label: "Pet's Name ",
              hintText: "Troy",
              controller: TextEditingController(),
              focusNode: FocusNode(),
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(FocusNode()),
              maxLines: 1,
            ),
            SizedBox(height: 10),
            CustomTextField(
              label: "Species Type of Pet",
              hintText: "Dog",
              controller: TextEditingController(),
              focusNode: FocusNode(),
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(FocusNode()),
              maxLines: 1,
            ),
            SizedBox(height: 10),
            CustomTextField(
              label: "Breed",
              hintText: "Toy Terrier",
              controller: TextEditingController(),
              focusNode: FocusNode(),
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(FocusNode()),
              maxLines: 1,
            ),
            SizedBox(height: 10),
            CustomTextField(
              label: "Size (optional)",
              hintText: "Weight",
              controller: TextEditingController(),
              focusNode: FocusNode(),
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(FocusNode()),
              maxLines: 1,
            ),
            SizedBox(height: 10),
            CustomTextField(
              label: "Birthday",
              controller: TextEditingController(),
              focusNode: FocusNode(),
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(FocusNode()),
              maxLines: 1,
            ),
            SizedBox(height: 10),
            CustomTextField(
              label: "Allergies",
              controller: TextEditingController(),
              focusNode: FocusNode(),
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(FocusNode()),
              maxLines: 1,
            ),
            SizedBox(height: 10),
            CustomTextField(
              label: "Current Medications",
              hintText: "None",
              controller: TextEditingController(),
              focusNode: FocusNode(),
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(FocusNode()),
              maxLines: 1,
            ),
            SizedBox(height: 10),
            CustomTextField(
              label: "Last Vaccinated Date",
              controller: TextEditingController(),
              focusNode: FocusNode(),
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(FocusNode()),
              maxLines: 1,
            ),
            SizedBox(height: 10),
            CustomTextField(
              label: "Favorite Toys",
              controller: TextEditingController(),
              focusNode: FocusNode(),
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(FocusNode()),
              maxLines: 1,
            ),

            const SizedBox(height: 20),
            const Align(
              alignment: Alignment.centerLeft,
              child: Text("What Best Describes Your Pet?"),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                _buildGenderToggle("Male", isMale, () => setState(() => isMale = true)),
                const SizedBox(width: 10),
                _buildGenderToggle("Female", !isMale, () => setState(() => isMale = false)),
              ],
            ),

            const SizedBox(height: 24),
            const Align(
              alignment: Alignment.centerLeft,
              child: Text("Additional Information"),
            ),
            const SizedBox(height: 12),
            ...switches.entries.map((entry) => _buildSwitch(entry.key, entry.value)),

            const SizedBox(height: 24),
            PrimaryButton(title: 'Save',onPressed: (){},),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitch(String title, bool value) {
    return SwitchListTile(
      title: Text(title),
      value: switches[title]!,
      activeColor:  AppColors.white,// Thumb color when ON
      activeTrackColor: const Color(0xFF1976D2), // Track color when ON
      inactiveThumbColor: AppColors.dividerColor, // Thumb when OFF
      inactiveTrackColor: AppColors.white, // Track when OFF
      onChanged: (val) {
        setState(() {
          switches[title] = val;
        });
      },
    );
  }

  Widget _buildGenderToggle(String label, bool isActive, VoidCallback onTap) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isActive ? Color(0xFF1976D2) : Colors.grey.shade300,
            borderRadius: BorderRadius.circular(8),
          ),
          alignment: Alignment.center,
          child: Text(
            label,
            style: TextStyle(
              color: isActive ? Colors.white : Colors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}

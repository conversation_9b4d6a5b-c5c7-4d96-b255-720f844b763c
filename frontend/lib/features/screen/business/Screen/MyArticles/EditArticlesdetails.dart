import 'package:flutter/material.dart';
import 'package:petcare/common/widgets/appbar/appbar.dart';
import '../../../../../common/widgets/Button/primarybutton.dart';
import '../../widgets/ImagePicker.dart';
import '../../widgets/custom_text_field.dart';

class EditArticlesDetails extends StatefulWidget {
  const EditArticlesDetails({super.key});

  @override
  State<EditArticlesDetails> createState() => _EditArticlesDetailsState();
}

class _EditArticlesDetailsState extends State<EditArticlesDetails> {
  // Controllers
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _categoryController = TextEditingController();
  final TextEditingController _bodyController = TextEditingController();
  final TextEditingController _tagsController = TextEditingController();
  final TextEditingController _recommendedProductsController = TextEditingController();

  // Focus Nodes
  final FocusNode _titleFocus = FocusNode();
  final FocusNode _categoryFocus = FocusNode();
  final FocusNode _bodyFocus = FocusNode();
  final FocusNode _tagsFocus = FocusNode();
  final FocusNode _recommendedProductsFocus = FocusNode();

  @override
  void dispose() {
    _titleController.dispose();
    _categoryController.dispose();
    _bodyController.dispose();
    _tagsController.dispose();
    _recommendedProductsController.dispose();

    _titleFocus.dispose();
    _categoryFocus.dispose();
    _bodyFocus.dispose();
    _tagsFocus.dispose();
    _recommendedProductsFocus.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: 'Edit Articles'),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            CustomTextField(
              label: 'Title',
              controller: _titleController,
              focusNode: _titleFocus,
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(_categoryFocus),
              maxLines: 1,
            ),
            const SizedBox(height: 20),

            // Category
            CustomTextField(
              label: 'Category',
              controller: _categoryController,
              focusNode: _categoryFocus,
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(_bodyFocus),
              maxLines: 2,
            ),
            const SizedBox(height: 20),

            // Body
            CustomTextField(
              label: 'Body',
              controller: _bodyController,
              focusNode: _bodyFocus,
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(_tagsFocus),
              maxLines: 9,
            ),
            const SizedBox(height: 20),

            // Tags
            CustomTextField(
              label: 'Tags',
              controller: _tagsController,
              focusNode: _tagsFocus,
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(_recommendedProductsFocus),
            ),
            const SizedBox(height: 20),

            // Recommended Products
            CustomTextField(
              label: 'Recommended Products',
              controller: _recommendedProductsController,
              focusNode: _recommendedProductsFocus,
              textInputAction: TextInputAction.done,
              onFieldSubmitted: () => FocusScope.of(context).unfocus(),
            ),
            const SizedBox(height: 20),

            // Image Picker
            const MultipleImagePicker(),
            const SizedBox(height: 20),

            // Save Button
            PrimaryButton(
              title: 'Save',
              onPressed: () {
                // TODO: Implement save logic
              },
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:petcare/features/screen/business/Screen/MyArticles/widgets/myarticlescard.dart';

import '../../../../../common/widgets/Button/primarybutton.dart';
import '../../../../../common/widgets/appbar/appbar.dart';
import '../../../../../utlis/constants/image_strings.dart';
import 'EditArticlesdetails.dart';


class MyArticles extends StatefulWidget {
  const MyArticles({super.key});

  @override
  State<MyArticles> createState() => _MyArticlesState();
}

class _MyArticlesState extends State<MyArticles> {
  List<Map<String, dynamic>> allArticles = [
    {
      'title': 'How to Adopt Outdoor Cats',
      'date': '5/20',
      'image': AppImages.dog2,
    },
    {
      'title': 'Dog Keeping You Up All Night?',
      'date': '5/20',
      'image': AppImages.dog2,
    },
    {
      'title': 'How to Adopt Outdoor Cats',
      'date': '5/20',
      'image': AppImages.dog1,
    },
    {
      'title': 'How to Adopt Outdoor Cats',
      'date': '5/20',
      'image':AppImages.dog1,
    },
  ];

  late List<Map<String, dynamic>> displayedArticles;

  @override
  void initState() {
    super.initState();
    displayedArticles = List.from(allArticles);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: 'My Articles'),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Sort & Filter
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  GestureDetector(
                    onTap: _showSortDialog,
                    child: const Row(
                      children: [
                        Icon(Icons.sort, color: Colors.grey),
                        SizedBox(width: 4),
                        Text('Sort', style: TextStyle(color: Colors.grey)),
                      ],
                    ),
                  ),
                  GestureDetector(
                    onTap: _showFilterDialog,
                    child: const Row(
                      children: [
                        Icon(Icons.filter_list, color: Colors.grey),
                        SizedBox(width: 4),
                        Text('Filter', style: TextStyle(color: Colors.grey)),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Button
              PrimaryButton(
                title: 'Create New Article',
                onPressed: () {
                  // Add navigation to AddNewArticle page
                },
              ),
              const SizedBox(height: 30),

              // Grid of Articles
              GridView.builder(
                itemCount: displayedArticles.length,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  mainAxisExtent: 235,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                ),
                itemBuilder: (context, index) {
                  final article = displayedArticles[index];
                  return ArticleCard(
                    title: article['title'],
                    date: article['date'],
                    imageUrl: article['image'],
                    onDelete: () {
                      setState(() {
                        displayedArticles.removeAt(index);
                      });
                    },
                    onEdit: () {
                      Get.to(() => EditArticlesDetails());
                      // Handle edit
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _sortProducts(bool ascending) {
    setState(() {
      displayedArticles.sort((a, b) =>
      ascending ? a['title'].compareTo(b['title']) : b['title'].compareTo(a['title']));
    });
  }

  void _filterProducts(String keyword) {
    setState(() {
      displayedArticles =
          allArticles.where((p) => p['title'].toLowerCase().contains(keyword.toLowerCase())).toList();
    });
  }

  void _resetFilter() {
    setState(() {
      displayedArticles = List.from(allArticles);
    });
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text("Sort by Title"),
        actions: [
          TextButton(
              onPressed: () {
                _sortProducts(true);
                Navigator.pop(context);
              },
              child: const Text("A-Z")),
          TextButton(
              onPressed: () {
                _sortProducts(false);
                Navigator.pop(context);
              },
              child: const Text("Z-A")),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    String keyword = '';
    showModalBottomSheet(
      context: context,
      builder: (_) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text("Filter by Keyword", style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 10),
              TextField(
                decoration: const InputDecoration(hintText: 'Enter keyword...'),
                onChanged: (value) => keyword = value,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      _resetFilter();
                      Navigator.pop(context);
                    },
                    child: const Text("Reset"),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      _filterProducts(keyword);
                      Navigator.pop(context);
                    },
                    child: const Text("Apply"),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}




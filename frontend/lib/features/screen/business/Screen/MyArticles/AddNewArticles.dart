import 'package:flutter/material.dart';

import '../../../../../common/widgets/Button/primarybutton.dart';
import '../../../../../common/widgets/appbar/appbar.dart';
import '../../widgets/ImagePicker.dart';
import '../../widgets/custom_text_field.dart';

class AddNewArticles extends StatefulWidget {
  const AddNewArticles({super.key});

  @override
  State<AddNewArticles> createState() => _AddNewArticlesState();
}

class _AddNewArticlesState extends State<AddNewArticles> {
  // Declare controllers
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _categoryController = TextEditingController();
  final TextEditingController _bodyController = TextEditingController();
  final TextEditingController _tagsController = TextEditingController();
  final TextEditingController _relatedProductsController = TextEditingController();

  // Declare focus nodes
  final FocusNode _titleFocus = FocusNode();
  final FocusNode _categoryFocus = FocusNode();
  final FocusNode _bodyFocus = FocusNode();
  final FocusNode _tagsFocus = FocusNode();
  final FocusNode _relatedProductsFocus = FocusNode();

  @override
  void dispose() {
    // Dispose controllers and focus nodes
    _titleController.dispose();
    _categoryController.dispose();
    _bodyController.dispose();
    _tagsController.dispose();
    _relatedProductsController.dispose();

    _titleFocus.dispose();
    _categoryFocus.dispose();
    _bodyFocus.dispose();
    _tagsFocus.dispose();
    _relatedProductsFocus.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: 'Add New Articles'),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            CustomTextField(
              label: 'Title',
              controller: _titleController,
              focusNode: _titleFocus,
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(_categoryFocus),
              maxLines: 1,
            ),
            const SizedBox(height: 16),

            // Category
            CustomTextField(
              label: 'Category',
              controller: _categoryController,
              focusNode: _categoryFocus,
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(_bodyFocus),
              maxLines: 2,
            ),
            const SizedBox(height: 16),

            // Body
            CustomTextField(
              label: 'Body',
              controller: _bodyController,
              focusNode: _bodyFocus,
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(_tagsFocus),
              maxLines: 9,
            ),
            const SizedBox(height: 16),

            // Tags
            CustomTextField(
              label: 'Tags',
              controller: _tagsController,
              focusNode: _tagsFocus,
              textInputAction: TextInputAction.next,
              onFieldSubmitted: () => FocusScope.of(context).requestFocus(_relatedProductsFocus),
              maxLines: 1,
            ),
            const SizedBox(height: 16),

            // Related Products
            CustomTextField(
              label: 'Related Products',
              controller: _relatedProductsController,
              focusNode: _relatedProductsFocus,
              textInputAction: TextInputAction.done,
              onFieldSubmitted: () => FocusScope.of(context).unfocus(),
              maxLines: 1,
            ),
            const SizedBox(height: 16),

            // Image Picker
            const MultipleImagePicker(),
            const SizedBox(height: 24),

            // Save Button
            PrimaryButton(
              title: "Save",
              onPressed: () {
                // Handle Save logic here
              },
            ),
          ],
        ),
      ),
    );
  }
}


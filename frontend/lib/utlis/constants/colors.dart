import 'package:flutter/material.dart';


class AppColors{

  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);

  static const Color primary = Color(0xFF383E56);
  static const Color secondary  = Color(0xFF070821);

  static  Color textPrimaryColor = Color(0xFF969292).withOpacity(0.10);
  static const Color textSecondary = Color(0xFF9E9E9E);
  static const Color dividerColor  = Color(0xFF979292);
  static const Color dottedColor = Color(0xFF4552CB);
  static const Color cart = Color(0xFF1976D2);
  static const Color divider = Color(0xFFE4E3F3);

  // Additional colors for error handling and UI states
  static const Color grey = Color(0xFF9E9E9E);
  static const Color error = Color(0xFFD32F2F);
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
}